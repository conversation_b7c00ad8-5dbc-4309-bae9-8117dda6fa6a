import { DeliverableEntity, DeliverableTypeEntity } from '../entities';
import { dataSource } from '../configs/typeorm.datasource';
import { mockData } from './mocks';
import { v4 as uuidv4 } from 'uuid';
import { DeliverableOwnerEntity } from 'src/entities/deliverables-owners.entity';

async function runSeed() {
  await dataSource.initialize();
  console.log('📦 Conectado ao banco de dados.');

  const deliverableRepo = dataSource.getRepository(DeliverableEntity);
  const deliverableOwnerRepo = dataSource.getRepository(DeliverableOwnerEntity);
  const deliverableTypeRepo = dataSource.getRepository(DeliverableTypeEntity);

  const typeCache = new Map<string, DeliverableTypeEntity>();
  const ownerCache = new Map<string, DeliverableOwnerEntity>();

  console.log('🚀 Inserindo Deliverables...');

  for (const item of mockData) {
    let deliverableType;
    const deliverableOwners = [];

    if (item.deliverableType.code) {
      deliverableType = typeCache.get(item.deliverableType.code);
    }

    if (!deliverableType) {
      deliverableType = deliverableTypeRepo.create({
        code: item.deliverableType.code
      });
      await deliverableTypeRepo.save(deliverableType);
      typeCache.set(item.deliverableType.code, deliverableType);
    }

    const deliverable = deliverableRepo.create({
      uid: uuidv4(),
      name: item.name,
      content: item.content,
      businessFunction: item.businessFunction,
      frequency: item.frequency,
      definition: item.definition,
      buLevelAggregation: item.buLevelAggregation,
      calculationMethod: item.calculationMethod,
      paValue: item.paValue,
      isActive: item.isActive ?? true,
      deliverableType: deliverableType,
      createdBy: uuidv4(),
      createdAt: new Date(),
      owners: []
    });

    await deliverableRepo.save(deliverable);

    if (item.owners?.length) {
      item.owners.map(async ({ uuid, email, globalId, name, positionTitle }) => {
        const deliverableOwner = ownerCache.get(uuid);

        if (!deliverableOwner) {
          await deliverableOwnerRepo.save({
            uid: uuid,
            email,
            globalId,
            name,
            positionTitle,
            deliverables: [deliverable],
            createdBy: uuidv4(),
            createdAt: new Date()
          });
          deliverableOwners.push(deliverableOwner);
        }
      });
    }
  }

  console.log('✅ Seed concluído com sucesso!');

  await dataSource.destroy();
}

runSeed().catch((err) => {
  console.error('❌ Erro ao executar seed:', err);
  process.exit(1);
});
