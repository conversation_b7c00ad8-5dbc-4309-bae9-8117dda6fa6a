import { <PERSON><PERSON><PERSON>, Column, ManyTo<PERSON><PERSON>, JoinC<PERSON>umn, ManyToMany, JoinTable } from 'typeorm';
import { DeliverableTypeEntity } from './deliverables-types.entity';
import { BaseEntity } from './base.entity';
import { DeliverableOwnerEntity } from './deliverables-owners.entity';

@Entity({ name: 'deliverables' })
export class DeliverableEntity extends BaseEntity {
  @Column({ type: 'nvarchar', length: 'MAX', nullable: true })
  buLevelAggregation: string;

  @Column()
  businessFunction: string;

  @Column({ type: 'nvarchar', length: 'MAX' })
  calculationMethod: string;

  @Column({ type: 'nvarchar', length: 'MAX', nullable: true })
  content: string;

  @Column({ type: 'datetime2', nullable: true })
  dateEnd: Date;

  @Column({ type: 'datetime2', nullable: true })
  dateStart: Date;

  @Column({ type: 'nvarchar', length: 'MAX' })
  definition: string;

  @Column({ type: 'nvarchar', nullable: true })
  frequency: string;

  @Column()
  isActive: boolean;

  @Column()
  name: string;

  @Column({ nullable: true })
  dataSource: string;

  @Column({ type: 'nvarchar', length: 'MAX', nullable: true })
  paValue: string;

  @ManyToOne(() => DeliverableTypeEntity, (type) => type.deliverables)
  @JoinColumn({ name: 'deliverables_types_code' })
  deliverableType: DeliverableTypeEntity;

  @ManyToMany(() => DeliverableEntity, (deliverable) => deliverable.deliverables)
  @JoinTable({ name: 'deliverables_deliverables' })
  deliverables: DeliverableEntity[];

  @ManyToMany(() => DeliverableOwnerEntity, (owner) => owner.deliverables, { eager: true })
  @JoinTable({ name: 'deliverables_deliverables_owners' })
  owners: DeliverableOwnerEntity[];

  // @ManyToOne(() => DeliverableEntity, (deliverable) => deliverable.deliverables)
  // @JoinColumn({ name: 'parent_deliverable_uid' })
  // parentDeliverable: DeliverableEntity;
}
