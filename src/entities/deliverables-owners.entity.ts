import { <PERSON><PERSON><PERSON>, Column, ManyToMany, JoinTable } from 'typeorm';
import { DeliverableEntity } from './deliverables.entity';
import { BaseEntity } from './base.entity';

@Entity({ name: 'deliverables_owners' })
export class DeliverableOwnerEntity extends BaseEntity {
  @Column()
  uid: string;

  @Column()
  email: string;

  @Column()
  globalId: number;

  @Column()
  name: string;

  @Column()
  positionTitle: string;

  @ManyToMany(() => DeliverableEntity, (deliverable) => deliverable.owners)
  @JoinTable({ name: 'deliverables_deliverables_owners' })
  deliverables: DeliverableEntity[];
}
